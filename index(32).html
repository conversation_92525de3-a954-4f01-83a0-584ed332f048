<html lang="ar" dir="rtl">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>برنامج متابعة الغيابات</title>
<script src="https://cdn.tailwindcss.com"></script>
<link
  rel="stylesheet"
  href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css"
/>
<link href="https://fonts.googleapis.com/css2?family=Cairo&display=swap" rel="stylesheet" />
<style>
  body {
    font-family: 'Cairo', sans-serif;
  }
  /* Scrollbar for days header and  body */
  .scrollbar-thin::-webkit-scrollbar {
    height: 6px;
  }
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: #a0aec0;
    border-radius: 3px;
  }
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #a0aec0 transparent;
  }
  /* Marquee animation */
  @keyframes marquee {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
  }
  .marquee {
    white-space: nowrap;
    overflow: hidden;
    box-sizing: border-box;
  }
  .marquee span {
    display: inline-block;
    padding-left: 100%;
    animation: marquee 15s linear infinite;
  }
  /* Scrollbar for stats table */
  #statsModal .table-container {
    max-height: 50vh;
    overflow-y: auto;
  }
  #statsTableBody td {
    color: black !important;
  }
  /* Sticky header for stats table */
  #statsModal table thead tr th {
    position: sticky;
    top: 0;
    background-color: #6b46c1;
    z-index: 20;
    color: black !important;
  }
  /* Scrollbar for stats table body */
  #statsTableBody::-webkit-scrollbar {
    width: 8px;
  }
  #statsTableBody::-webkit-scrollbar-thumb {
    background-color: #a78bfa;
    border-radius: 4px;
  }
  #statsTableBody {
    scrollbar-width: thin;
    scrollbar-color: #a78bfa transparent;
  }
  /* Stats table styling */
  #statsModal table {
    table-layout: fixed;
  }
  #statsModal table th,
  #statsModal table td {
    padding: 8px 4px;
    text-align: center;
    vertical-align: middle;
  }
</style>
</head>
<body class="bg-gradient-to-r from-purple-600 via-blue-700 to-indigo-700 min-h-screen flex flex-col text-white">

<!-- Login Screen -->
<div id="loginScreen" class="fixed inset-0 bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center z-50">
  <div class="bg-white bg-opacity-10 backdrop-blur-lg rounded-2xl shadow-2xl p-8 w-full max-w-md mx-4 border border-white border-opacity-20">
    <div class="text-center mb-8">
      <div class="bg-white bg-opacity-20 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
        <i class="fas fa-user-shield text-3xl text-white"></i>
      </div>
      <h1 class="text-3xl font-bold text-white mb-2">تسجيل الدخول</h1>
      <p class="text-gray-200">برنامج متابعة الغيابات</p>
    </div>

    <form id="loginForm" class="space-y-6">
      <div>
        <label for="username" class="block text-sm font-medium text-gray-200 mb-2">اسم المستخدم</label>
        <div class="relative">
          <input type="text" id="username" name="username" required
                 class="w-full px-4 py-3 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition duration-300"
                 placeholder="أدخل اسم المستخدم" />
          <i class="fas fa-user absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-300"></i>
        </div>
      </div>

      <div>
        <label for="password" class="block text-sm font-medium text-gray-200 mb-2">كلمة المرور</label>
        <div class="relative">
          <input type="password" id="password" name="password" required
                 class="w-full px-4 py-3 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition duration-300"
                 placeholder="أدخل كلمة المرور" />
          <i class="fas fa-lock absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-300"></i>
        </div>
      </div>

      <div id="loginError" class="hidden bg-red-500 bg-opacity-20 border border-red-400 text-red-200 px-4 py-3 rounded-lg text-sm">
        <i class="fas fa-exclamation-triangle mr-2"></i>
        <span id="loginErrorText">خطأ في اسم المستخدم أو كلمة المرور</span>
      </div>

      <button type="submit" class="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-4 rounded-lg transition duration-300 transform hover:scale-105 shadow-lg">
        <i class="fas fa-sign-in-alt mr-2"></i>
        دخول
      </button>
    </form>


  </div>
</div>

<!-- Activity Log Modal -->
<div id="activityModal" class="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center p-4 hidden z-50">
  <div class="bg-white rounded-lg shadow-lg max-w-4xl w-full p-6 relative max-h-[80vh] overflow-y-auto">
    <h2 class="text-2xl font-bold mb-4 text-center text-indigo-700">سجل النشاطات الأخيرة</h2>
    <div id="activityList" class="space-y-3 mb-6">
      <!-- Activities will be populated here -->
    </div>
    <div class="flex justify-center gap-3">
      <button id="clearActivityBtn" class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded shadow-md transition duration-300">
        <i class="fas fa-trash"></i> مسح السجل
      </button>
      <button id="closeActivityBtn" class="bg-indigo-700 hover:bg-indigo-800 text-white px-6 py-2 rounded shadow-md transition duration-300">إغلاق</button>
    </div>
  </div>
</div>

<!-- User Management Modal -->
<div id="userManagementModal" class="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center p-4 hidden z-50">
  <div class="bg-white rounded-lg shadow-lg max-w-5xl w-full p-6 relative max-h-[85vh] overflow-y-auto">
    <h2 class="text-2xl font-bold mb-6 text-center text-indigo-700">إدارة المستخدمين</h2>

    <!-- Add User Form -->
    <div class="bg-gray-50 rounded-lg p-4 mb-6">
      <h3 class="text-lg font-semibold mb-4 text-gray-700">إضافة مستخدم جديد</h3>
      <form id="addUserForm" class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">اسم المستخدم</label>
          <input type="text" id="newUsername" required class="w-full border border-gray-300 rounded px-3 py-2 text-black focus:outline-none focus:ring-2 focus:ring-indigo-500" />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">كلمة المرور</label>
          <input type="password" id="newPassword" required class="w-full border border-gray-300 rounded px-3 py-2 text-black focus:outline-none focus:ring-2 focus:ring-indigo-500" />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">الاسم المعروض</label>
          <input type="text" id="newDisplayName" required class="w-full border border-gray-300 rounded px-3 py-2 text-black focus:outline-none focus:ring-2 focus:ring-indigo-500" />
        </div>
        <div class="flex items-end">
          <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded transition duration-300">
            <i class="fas fa-user-plus"></i> إضافة
          </button>
        </div>
      </form>
    </div>

    <!-- Users List -->
    <div class="overflow-x-auto">
      <table class="w-full border-collapse border border-gray-300">
        <thead class="bg-indigo-700 text-white">
          <tr>
            <th class="border border-indigo-800 px-4 py-3">اسم المستخدم</th>
            <th class="border border-indigo-800 px-4 py-3">الاسم المعروض</th>
            <th class="border border-indigo-800 px-4 py-3">الصلاحية</th>
            <th class="border border-indigo-800 px-4 py-3">كلمة المرور</th>
            <th class="border border-indigo-800 px-4 py-3">الإجراءات</th>
          </tr>
        </thead>
        <tbody id="usersTableBody" class="text-center">
          <!-- Users will be populated here -->
        </tbody>
      </table>
    </div>

    <div class="mt-6 flex justify-center">
      <button id="closeUserManagementBtn" class="bg-indigo-700 hover:bg-indigo-800 text-white px-8 py-2 rounded shadow-md transition duration-300">إغلاق</button>
    </div>
  </div>
</div>

<!-- Edit User Modal -->
<div id="editUserModal" class="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center p-4 hidden z-50">
  <div class="bg-white rounded-lg shadow-lg max-w-md w-full p-6 relative">
    <h2 class="text-2xl font-bold mb-6 text-center text-indigo-700">تعديل المستخدم</h2>
    <form id="editUserForm" class="space-y-4">
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">اسم المستخدم</label>
        <input type="text" id="editUsername" readonly class="w-full border border-gray-300 rounded px-3 py-2 text-black bg-gray-100" />
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">كلمة المرور الجديدة</label>
        <input type="password" id="editPassword" class="w-full border border-gray-300 rounded px-3 py-2 text-black focus:outline-none focus:ring-2 focus:ring-indigo-500" />
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">الاسم المعروض</label>
        <input type="text" id="editDisplayName" required class="w-full border border-gray-300 rounded px-3 py-2 text-black focus:outline-none focus:ring-2 focus:ring-indigo-500" />
      </div>
      <div class="flex justify-end gap-3 mt-6">
        <button type="button" id="cancelEditUserBtn" class="px-5 py-2 rounded border border-gray-400 hover:bg-gray-100 text-gray-700 transition duration-300">إلغاء</button>
        <button type="submit" class="bg-indigo-700 text-white px-5 py-2 rounded hover:bg-indigo-800 transition duration-300">حفظ التعديلات</button>
      </div>
    </form>
  </div>
</div>

<!-- Inquiry Modal -->
<div id="inquiryModal" class="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center p-4 hidden z-50">
  <div class="bg-white rounded-lg shadow-lg max-w-md w-full p-6 relative">
    <h2 class="text-2xl font-bold mb-6 text-center text-indigo-700">ربط ملف بالموظف</h2>
    <div class="mb-6">
      <div class="bg-gray-50 rounded-lg p-4 mb-4">
        <h3 class="font-semibold text-gray-700 mb-2">بيانات الموظف:</h3>
        <div id="employeeInfo" class="space-y-1 text-sm text-gray-600">
          <!-- Employee info will be populated here -->
        </div>
      </div>

      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">ملف الاستفسار:</label>
          <div class="flex gap-2">
            <input type="text" id="inquiryLink" placeholder="اختر ملف (Word, PDF, Excel, إلخ)" readonly
                   class="flex-1 border border-gray-300 rounded px-3 py-2 text-black bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500" />
            <button type="button" id="browseFileBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition duration-300" title="اختيار ملف">
              <i class="fas fa-file-alt"></i> اختيار ملف
            </button>
          </div>
          <p class="text-xs text-gray-500 mt-1">اختر ملف Word أو PDF أو Excel أو أي ملف آخر. بعد الحفظ، الضغط على الأيقونة سيفتح الملف مباشرة.</p>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات:</label>
          <textarea id="inquiryNotes" rows="3" placeholder="أدخل ملاحظات إضافية (اختياري)"
                    class="w-full border border-gray-300 rounded px-3 py-2 text-black focus:outline-none focus:ring-2 focus:ring-indigo-500"></textarea>
        </div>
      </div>
    </div>

    <div class="flex justify-end gap-3">
      <button type="button" id="cancelInquiryBtn" class="px-5 py-2 rounded border border-gray-400 hover:bg-gray-100 text-gray-700 transition duration-300">إلغاء</button>
      <button type="button" id="openLinkBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-5 py-2 rounded transition duration-300">
        <i class="fas fa-external-link-alt"></i> فتح الملف
      </button>
      <button type="button" id="saveInquiryBtn" class="bg-indigo-700 text-white px-5 py-2 rounded hover:bg-indigo-800 transition duration-300">
        <i class="fas fa-save"></i> حفظ الربط
      </button>
    </div>
  </div>
</div>

<!-- Main App Content -->

<header class="relative bg-transparent p-4 flex flex-col md:flex-row md:items-center md:justify-between gap-3 md:gap-0">
  <div class="flex flex-col md:flex-row md:items-center gap-2 md:gap-6 flex-1 overflow-hidden">
    <div class="marquee text-lg font-semibold whitespace-nowrap">
      <span>هذا البرنامج من إعداد ح ش/ لعجاج إسماعيل</span>
    </div>
  </div>
  <div class="flex items-center gap-4">
    <div class="text-sm font-semibold">
      <span>مرحباً، </span><span id="currentUserName" class="text-yellow-300"></span>
    </div>
    <button id="logoutBtn" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded flex items-center gap-2 shadow-md transition duration-300">
      <i class="fas fa-sign-out-alt"></i> خروج
    </button>
  </div>
</header>

<header class="bg-white bg-opacity-20 backdrop-blur-md shadow-lg p-4 text-center text-3xl font-extrabold tracking-wide rounded-b-lg select-none text-indigo-100">
  برنامج متابعة الغيابات
</header>

<main class="flex-grow p-4 max-w-full overflow-x-auto bg-white bg-opacity-90 rounded-lg shadow-lg mx-4 my-6 text-gray-800">

  <section class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
    <div class="flex items-center gap-2 flex-wrap">
      <label for="monthSelect" class="font-semibold text-lg text-gray-700">اختر الشهر:</label>
      <select id="monthSelect" class="border border-gray-300 rounded px-3 py-1 text-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
      </select>
      <select id="yearSelect" class="border border-gray-300 rounded px-3 py-1 text-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
      </select>
    </div>
    <div class="flex gap-2 flex-wrap items-center">
      <input type="text" id="searchMainInput" placeholder="ابحث بالاسم، اللقب أو رقم الذاتية" class="border border-gray-300 rounded px-4 py-2 text-lg focus:outline-none focus:ring-2 focus:ring-indigo-500" />
      <button id="searchMainBtn" class="bg-indigo-700 hover:bg-indigo-800 text-white px-4 py-2 rounded flex items-center gap-2 shadow-md transition duration-300">
        <i class="fas fa-search"></i> بحث
      </button>
      <button id="statsBtn" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded flex items-center gap-2 shadow-md transition duration-300">
        <i class="fas fa-chart-bar"></i> الإحصائيات
      </button>
      <button id="addEmployeeBtn" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded flex items-center gap-2 shadow-md transition duration-300">
        <i class="fas fa-user-plus"></i> إضافة موظف جديد
      </button>
      <button id="activityBtn" class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded flex items-center gap-2 shadow-md transition duration-300">
        <i class="fas fa-history"></i> سجل النشاطات
      </button>
      <button id="manageUsersBtn" class="bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded flex items-center gap-2 shadow-md transition duration-300" style="display: none;">
        <i class="fas fa-users-cog"></i> إدارة المستخدمين
      </button>
    </div>
  </section>

  <section class="overflow-auto border border-gray-300 rounded shadow bg-white">
    <table class="min-w-max w-full table-fixed border-collapse" id="attendanceTable" aria-label="جدول متابعة الغيابات">
      <thead class="bg-indigo-700 text-white sticky top-0 z-10">
        <tr>
          <th class="border border-indigo-800 px-2 py-1 w-28 text-center">الاسم</th>
          <th class="border border-indigo-800 px-2 py-1 w-28 text-center">اللقب</th>
          <th class="border border-indigo-800 px-2 py-1 w-28 text-center">الرتبة</th>
          <th class="border border-indigo-800 px-2 py-1 w-24 text-center">رقم الذاتية</th>
          <!-- Days header will be generated by JS -->
          <th class="border border-indigo-800 px-2 py-1 w-20 text-center">غيابات</th>
          <th class="border border-indigo-800 px-2 py-1 w-20 text-center">خصم</th>
          <th class="border border-indigo-800 px-2 py-1 w-20 text-center">حفظ</th>
          <th class="border border-indigo-800 px-2 py-1 w-24 text-center">إجراءات</th>
        </tr>
      </thead>
      <tbody id="tableBody" class="text-center">
      </tbody>
    </table>
  </section>

</main>

<!-- Modal for adding/editing employee -->
<div id="employeeModal" class="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center p-4 hidden z-50">
  <div class="bg-white rounded-lg shadow-lg max-w-md w-full p-6 relative">
    <h2 class="text-2xl font-bold mb-6 text-center text-indigo-700">إضافة موظف جديد</h2>
    <form id="employeeForm" class="flex flex-col gap-5" autocomplete="off">
      <div>
        <label for="firstName" class="block mb-1 font-semibold text-gray-700">الاسم</label>
        <input type="text" id="firstName" name="firstName" required class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black" />
      </div>
      <div>
        <label for="lastName" class="block mb-1 font-semibold text-gray-700">اللقب</label>
        <input type="text" id="lastName" name="lastName" required class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black" />
      </div>
      <div>
        <label for="rank" class="block mb-1 font-semibold text-gray-700">الرتبة</label>
        <select id="rank" name="rank" required class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 cursor-pointer text-black">
          <option value="" disabled selected>اختر الرتبة</option>
          <option value="عميد ش">عميد ش</option>
          <option value="محا ش">محا ش</option>
          <option value="ض ش">ض ش</option>
          <option value="ر ض ش">ر ض ش</option>
          <option value="ح أ ش">ح أ ش</option>
          <option value="ح ش">ح ش</option>
          <option value="ع ش">ع ش</option>
          <option value="ع شبهي">ع شبهي</option>
        </select>
      </div>
      <div>
        <label for="idNumber" class="block mb-1 font-semibold text-gray-700">رقم الذاتية</label>
        <input type="text" id="idNumber" name="idNumber" required class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black" />
      </div>
      <div class="flex justify-end gap-3 mt-6">
        <button type="button" id="cancelEmployeeBtn" class="px-5 py-2 rounded border border-gray-400 hover:bg-gray-100 text-gray-700 transition duration-300">إلغاء</button>
        <button type="submit" class="bg-indigo-700 text-white px-5 py-2 rounded hover:bg-indigo-800 transition duration-300">حفظ</button>
      </div>
    </form>
    <button id="closeModalBtn" class="absolute top-3 left-3 text-gray-500 hover:text-gray-700 text-3xl font-bold" aria-label="إغلاق النافذة">&times;</button>
  </div>
</div>

<!-- Modal for statistics -->
<div id="statsModal" class="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center p-4 hidden z-50 overflow-auto">
  <div class="bg-white rounded-lg shadow-lg max-w-7xl w-full p-6 relative max-h-[90vh] overflow-y-auto">
    <h2 class="text-2xl font-bold mb-4 text-center text-indigo-700">إحصائيات الغيابات</h2>
    <div class="mb-4 flex justify-center gap-2 flex-wrap">
      <input type="text" id="searchStatsInput" placeholder="ابحث بالاسم، اللقب، الرتبة أو رقم الذاتية" class="w-full max-w-xl border border-gray-300 rounded px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black" />
      <button id="searchStatsBtn" class="bg-indigo-700 hover:bg-indigo-800 text-white px-6 py-2 rounded shadow-md transition duration-300 flex items-center gap-2">
        <i class="fas fa-search"></i> بحث
      </button>
    </div>
    <div class="table-container">
      <table class="w-full border-collapse border border-gray-300 text-center" dir="rtl">
        <thead class="bg-indigo-700 sticky top-0 z-10">
          <tr>
            <th class="border border-indigo-800 px-4 py-3 text-black">الاسم</th>
            <th class="border border-indigo-800 px-4 py-3 text-black">اللقب</th>
            <th class="border border-indigo-800 px-4 py-3 text-black">الرتبة</th>
            <th class="border border-indigo-800 px-4 py-3 text-black">رقم الذاتية</th>
            <th class="border border-indigo-800 px-4 py-3 text-black">خصم الأسبوع</th>
            <th class="border border-indigo-800 px-4 py-3 text-black">حفظ الأسبوع</th>
            <th class="border border-indigo-800 px-4 py-3 text-black">خصم الشهر</th>
            <th class="border border-indigo-800 px-4 py-3 text-black">حفظ الشهر</th>
            <th class="border border-indigo-800 px-4 py-3 text-black">خصم 3 أشهر</th>
            <th class="border border-indigo-800 px-4 py-3 text-black">حفظ 3 أشهر</th>
            <th class="border border-indigo-800 px-4 py-3 text-black">خصم السنة</th>
            <th class="border border-indigo-800 px-4 py-3 text-black">حفظ السنة</th>
          </tr>
        </thead>
        <tbody id="statsTableBody" class="text-center">
        </tbody>
      </table>
    </div>
    <div class="mt-6 flex justify-center">
      <button id="closeStatsBtn" class="bg-indigo-700 hover:bg-indigo-800 text-white px-8 py-2 rounded shadow-md transition duration-300">إغلاق</button>
    </div>
  </div>
</div>

<script>
  // User Management System
  const DEFAULT_USERS = {
    'ISMAIL': { password: '2026', role: 'admin', displayName: 'إسماعيل' },
    'USER1': { password: 'user1', role: 'user', displayName: 'المستخدم الأول' },
    'USER2': { password: 'user2', role: 'user', displayName: 'المستخدم الثاني' }
  };

  let currentUser = null;

  // Load users from localStorage or use defaults
  function loadUsers() {
    const savedUsers = localStorage.getItem('systemUsers');
    if (savedUsers) {
      return JSON.parse(savedUsers);
    } else {
      // First time, save default users
      localStorage.setItem('systemUsers', JSON.stringify(DEFAULT_USERS));
      return DEFAULT_USERS;
    }
  }

  // Save users to localStorage
  function saveUsers(users) {
    localStorage.setItem('systemUsers', JSON.stringify(users));
  }

  // Get current users
  function getUsers() {
    return loadUsers();
  }

  // Activity Logging System
  function logActivity(action, details = '') {
    if (!currentUser) return;

    const activities = JSON.parse(localStorage.getItem('userActivities') || '[]');
    const now = new Date();

    // Format date and time in French
    const dateOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    };
    const timeOptions = {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    };

    const activity = {
      id: uuidv4(),
      user: currentUser.username,
      displayName: currentUser.displayName,
      action: action,
      details: details,
      timestamp: now.toISOString(),
      date: now.toLocaleDateString('fr-FR', dateOptions),
      time: now.toLocaleTimeString('fr-FR', timeOptions)
    };

    activities.unshift(activity); // Add to beginning

    // Keep only last 100 activities
    if (activities.length > 100) {
      activities.splice(100);
    }

    localStorage.setItem('userActivities', JSON.stringify(activities));
  }

  // Get recent activities for display
  function getRecentActivities(limit = 20) {
    const activities = JSON.parse(localStorage.getItem('userActivities') || '[]');
    return activities.slice(0, limit);
  }

  // Login System
  function handleLogin(username, password) {
    const users = getUsers();
    const user = users[username.toUpperCase()];
    if (user && user.password === password) {
      currentUser = {
        username: username.toUpperCase(),
        displayName: user.displayName,
        role: user.role
      };

      localStorage.setItem('currentUser', JSON.stringify(currentUser));
      logActivity('تسجيل دخول', 'دخل إلى النظام');

      return true;
    }
    return false;
  }

  // Logout System
  function handleLogout() {
    if (currentUser) {
      logActivity('تسجيل خروج', 'خرج من النظام');
      currentUser = null;
      localStorage.removeItem('currentUser');
    }
  }

  // Check if user is logged in
  function checkAuth() {
    const savedUser = localStorage.getItem('currentUser');
    if (savedUser) {
      currentUser = JSON.parse(savedUser);
      return true;
    }
    return false;
  }

  // Show recent activities to new user
  function showRecentActivitiesIfNeeded() {
    const lastLoginUser = localStorage.getItem('lastLoginUser');
    if (lastLoginUser && lastLoginUser !== currentUser.username) {
      const recentActivities = getRecentActivities(10);
      if (recentActivities.length > 0) {
        setTimeout(() => {
          showActivityModal();
        }, 1000);
      }
    }
    localStorage.setItem('lastLoginUser', currentUser.username);
  }

  // Utility to generate UUID v4
  function uuidv4() {
    return ([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g, c =>
      (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
    );
  }

  // Get days in month
  function getDaysInMonth(year, month) {
    return new Date(year, month, 0).getDate();
  }

  // Format month key YYYY-MM
  function formatMonthKey(year, month) {
    return `${year}-${month.toString().padStart(2, "0")}`;
  }

  // Load data from localStorage
  function loadData() {
    const data = localStorage.getItem("attendanceData");
    return data ? JSON.parse(data) : {};
  }

  // Save data to localStorage
  function saveData(data) {
    localStorage.setItem("attendanceData", JSON.stringify(data));
  }

  // Global state
  let attendanceData = loadData();
  let currentYear, currentMonth;
  let daysInCurrentMonth;

  // DOM references
  const loginScreen = document.getElementById("loginScreen");
  const loginForm = document.getElementById("loginForm");
  const loginError = document.getElementById("loginError");
  const loginErrorText = document.getElementById("loginErrorText");
  const currentUserName = document.getElementById("currentUserName");
  const logoutBtn = document.getElementById("logoutBtn");
  const activityBtn = document.getElementById("activityBtn");
  const activityModal = document.getElementById("activityModal");
  const activityList = document.getElementById("activityList");
  const closeActivityBtn = document.getElementById("closeActivityBtn");
  const clearActivityBtn = document.getElementById("clearActivityBtn");

  const manageUsersBtn = document.getElementById("manageUsersBtn");
  const userManagementModal = document.getElementById("userManagementModal");
  const closeUserManagementBtn = document.getElementById("closeUserManagementBtn");
  const addUserForm = document.getElementById("addUserForm");
  const usersTableBody = document.getElementById("usersTableBody");
  const editUserModal = document.getElementById("editUserModal");
  const editUserForm = document.getElementById("editUserForm");
  const cancelEditUserBtn = document.getElementById("cancelEditUserBtn");

  const inquiryModal = document.getElementById("inquiryModal");
  const cancelInquiryBtn = document.getElementById("cancelInquiryBtn");
  const openLinkBtn = document.getElementById("openLinkBtn");
  const saveInquiryBtn = document.getElementById("saveInquiryBtn");
  const browseFileBtn = document.getElementById("browseFileBtn");
  const inquiryLink = document.getElementById("inquiryLink");
  const inquiryNotes = document.getElementById("inquiryNotes");
  const employeeInfo = document.getElementById("employeeInfo");

  const monthSelect = document.getElementById("monthSelect");
  const yearSelect = document.getElementById("yearSelect");
  const tableBody = document.getElementById("tableBody");
  const attendanceTable = document.getElementById("attendanceTable");
  const addEmployeeBtn = document.getElementById("addEmployeeBtn");
  const employeeModal = document.getElementById("employeeModal");
  const employeeForm = document.getElementById("employeeForm");
  const cancelEmployeeBtn = document.getElementById("cancelEmployeeBtn");
  const closeModalBtn = document.getElementById("closeModalBtn");
  const searchMainInput = document.getElementById("searchMainInput");
  const searchMainBtn = document.getElementById("searchMainBtn");

  const statsBtn = document.getElementById("statsBtn");
  const statsModal = document.getElementById("statsModal");
  const statsTableBody = document.getElementById("statsTableBody");
  const closeStatsBtn = document.getElementById("closeStatsBtn");
  const searchStatsInput = document.getElementById("searchStatsInput");
  const searchStatsBtn = document.getElementById("searchStatsBtn");

  // Initialize month and year selects
  function initMonthYearSelectors() {
    const now = new Date();
    currentYear = now.getFullYear();
    currentMonth = now.getMonth() + 1;

    // Years range: 2025 to 3050
    for (let y = 2025; y <= 3050; y++) {
      const option = document.createElement("option");
      option.value = y;
      option.textContent = y;
      if (y === currentYear) option.selected = true;
      yearSelect.appendChild(option);
    }

    // Months French names
    const months = [
      "Janvier",
      "Février",
      "Mars",
      "Avril",
      "Mai",
      "Juin",
      "Juillet",
      "Août",
      "Septembre",
      "Octobre",
      "Novembre",
      "Décembre",
    ];

    months.forEach((m, i) => {
      const option = document.createElement("option");
      option.value = i + 1;
      option.textContent = m;
      if (i + 1 === currentMonth) option.selected = true;
      monthSelect.appendChild(option);
    });
  }

  // Render days header
  function renderDaysHeader() {
    // Remove old day headers if any
    const oldDayHeaders = attendanceTable.querySelectorAll("thead tr th.day-header");
    oldDayHeaders.forEach((th) => th.remove());

    daysInCurrentMonth = getDaysInMonth(currentYear, currentMonth);

    // Insert day headers after the 4th column (index 3)
    const headerRow = attendanceTable.querySelector("thead tr");
    for (let day = 1; day <= daysInCurrentMonth; day++) {
      const th = document.createElement("th");
      th.className = "border border-indigo-800 px-1 py-1 w-12 day-header";
      th.textContent = day;
      headerRow.insertBefore(th, headerRow.children[4 + day - 1] || headerRow.children[4]);
    }
  }

  // Render table body with employees and their attendance, filtered by search term
  function renderTableBody(filter = "") {
    tableBody.innerHTML = "";
    const monthKey = formatMonthKey(currentYear, currentMonth);
    const monthData = attendanceData[monthKey];
    if (!monthData || !monthData.employees.length) {
      const tr = document.createElement("tr");
      const td = document.createElement("td");
      td.colSpan = 4 + daysInCurrentMonth + 4;
      td.className = "py-6 text-center text-gray-500";
      td.textContent = "لا يوجد موظفين لهذا الشهر";
      tr.appendChild(td);
      tableBody.appendChild(tr);
      return;
    }

    const filterLower = filter.trim().toLowerCase();

    const filteredEmployees = monthData.employees.filter(emp => {
      if (!filterLower) return true;
      return (
        emp.firstName.toLowerCase().includes(filterLower) ||
        emp.lastName.toLowerCase().includes(filterLower) ||
        emp.idNumber.toLowerCase().includes(filterLower)
      );
    });

    if (filteredEmployees.length === 0) {
      const tr = document.createElement("tr");
      const td = document.createElement("td");
      td.colSpan = 4 + daysInCurrentMonth + 4;
      td.className = "py-6 text-center text-gray-500";
      td.textContent = "لا توجد نتائج مطابقة للبحث";
      tr.appendChild(td);
      tableBody.appendChild(tr);
      return;
    }

    filteredEmployees.forEach((emp) => {
      const tr = document.createElement("tr");
      tr.className = "hover:bg-indigo-50 text-gray-800";

      // Name
      const tdFirstName = document.createElement("td");
      tdFirstName.className = "border border-gray-300 px-2 py-1 text-center";
      tdFirstName.textContent = emp.firstName;
      tr.appendChild(tdFirstName);

      // Last name
      const tdLastName = document.createElement("td");
      tdLastName.className = "border border-gray-300 px-2 py-1 text-center";
      tdLastName.textContent = emp.lastName;
      tr.appendChild(tdLastName);

      // Rank
      const tdRank = document.createElement("td");
      tdRank.className = "border border-gray-300 px-2 py-1 text-center";
      tdRank.textContent = emp.rank;
      tr.appendChild(tdRank);

      // ID Number
      const tdIdNumber = document.createElement("td");
      tdIdNumber.className = "border border-gray-300 px-2 py-1 text-center";
      tdIdNumber.textContent = emp.idNumber;
      tr.appendChild(tdIdNumber);

      // Days dropdowns
      for (let day = 1; day <= daysInCurrentMonth; day++) {
        const tdDay = document.createElement("td");
        tdDay.className = "border border-gray-300 px-1 py-1 text-center";

        const select = document.createElement("select");
        select.className = "w-16 text-center cursor-pointer rounded border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500";
        select.title = "حالة الغياب";
        select.dataset.empId = emp.id;
        select.dataset.day = day;

        // Options: empty, absent, deducted, saved
        const options = [
          { value: "none", text: "" },
          { value: "absent", text: "غياب" },
          { value: "deducted", text: "خصم" },
          { value: "saved", text: "حفظ" },
        ];

        options.forEach(opt => {
          const option = document.createElement("option");
          option.value = opt.value;
          option.textContent = opt.text;
          select.appendChild(option);
        });

        // Determine current value
        const dayData = emp.days?.[day] || { absent: false, deducted: false, saved: false };
        let val = "none";
        if (dayData.absent) {
          if (dayData.deducted) val = "deducted";
          else if (dayData.saved) val = "saved";
          else val = "absent";
        }
        select.value = val;

        tdDay.appendChild(select);
        tr.appendChild(tdDay);
      }

      // Summary columns: total absent, total deducted, total saved
      const totalAbsent = Object.values(emp.days || {}).filter((d) => d.absent).length;
      const totalDeducted = Object.values(emp.days || {}).filter((d) => d.deducted).length;
      const totalSaved = Object.values(emp.days || {}).filter((d) => d.saved).length;

      const tdAbsentSum = document.createElement("td");
      tdAbsentSum.className = "border border-gray-300 px-2 py-1 text-center font-semibold text-red-600";
      tdAbsentSum.textContent = totalAbsent;
      tr.appendChild(tdAbsentSum);

      const tdDeductedSum = document.createElement("td");
      tdDeductedSum.className = "border border-gray-300 px-2 py-1 text-center font-semibold text-yellow-600";
      tdDeductedSum.textContent = totalDeducted;
      tr.appendChild(tdDeductedSum);

      const tdSavedSum = document.createElement("td");
      tdSavedSum.className = "border border-gray-300 px-2 py-1 text-center font-semibold text-green-600";
      tdSavedSum.textContent = totalSaved;
      tr.appendChild(tdSavedSum);

      // Actions: Edit, Delete, Inquiry
      const tdActions = document.createElement("td");
      tdActions.className = "border border-gray-300 px-2 py-1 text-center flex justify-center gap-2";

      const editBtn = document.createElement("button");
      editBtn.className = "text-indigo-600 hover:text-indigo-800 transition duration-300";
      editBtn.title = "تعديل بيانات الموظف";
      editBtn.innerHTML = '<i class="fas fa-edit"></i>';
      editBtn.addEventListener("click", () => openEditEmployeeModal(emp.id));

      const deleteBtn = document.createElement("button");
      deleteBtn.className = "text-red-600 hover:text-red-800 transition duration-300";
      deleteBtn.title = "حذف الموظف";
      deleteBtn.innerHTML = '<i class="fas fa-trash-alt"></i>';
      deleteBtn.addEventListener("click", () => deleteEmployee(emp.id));

      const inquiryBtn = document.createElement("button");
      inquiryBtn.className = "text-green-600 hover:text-green-800 transition duration-300";
      inquiryBtn.title = "فتح ملف الموظف - انقر بالزر الأيمن لربط ملف جديد";
      inquiryBtn.innerHTML = '<i class="fas fa-question-circle"></i>';
      inquiryBtn.addEventListener("click", () => openEmployeeInquiry(emp.id));
      inquiryBtn.addEventListener("contextmenu", (e) => {
        e.preventDefault();
        openInquiryModal(emp.id);
      });

      tdActions.appendChild(editBtn);
      tdActions.appendChild(deleteBtn);
      tdActions.appendChild(inquiryBtn);

      tr.appendChild(tdActions);

      tableBody.appendChild(tr);
    });
  }

  // Save dropdown change
  function onSelectChange(e) {
    const select = e.target;
    if (select.tagName !== "SELECT") return;
    const empId = select.dataset.empId;
    const day = select.dataset.day;
    if (!empId || !day) return;

    const monthKey = formatMonthKey(currentYear, currentMonth);
    if (!attendanceData[monthKey]) return;
    const emp = attendanceData[monthKey].employees.find((e) => e.id === empId);
    if (!emp) return;

    if (!emp.days) emp.days = {};
    if (!emp.days[day]) emp.days[day] = { absent: false, deducted: false, saved: false };

    // Reset all
    emp.days[day].absent = false;
    emp.days[day].deducted = false;
    emp.days[day].saved = false;

    // Set based on select value
    switch (select.value) {
      case "none":
        // all false
        break;
      case "absent":
        emp.days[day].absent = true;
        break;
      case "deducted":
        emp.days[day].absent = true;
        emp.days[day].deducted = true;
        break;
      case "saved":
        emp.days[day].absent = true;
        emp.days[day].saved = true;
        break;
    }

    // Log attendance change
    const statusText = select.value === 'none' ? 'حضور' :
                      select.value === 'absent' ? 'غياب' :
                      select.value === 'deducted' ? 'خصم' : 'حفظ';
    logActivity('تعديل حضور', `تم تعديل حضور ${emp.firstName} ${emp.lastName} لليوم ${day}: ${statusText}`);

    saveData(attendanceData);
    renderTableBody(searchMainInput.value);
    checkAbsenceWarnings();
  }

  // Add employee modal open
  function openAddEmployeeModal() {
    employeeForm.reset();
    employeeModal.classList.remove("hidden");
    employeeModal.dataset.editingId = "";
  }

  // Edit employee modal open
  function openEditEmployeeModal(empId) {
    const monthKey = formatMonthKey(currentYear, currentMonth);
    const emp = attendanceData[monthKey].employees.find((e) => e.id === empId);
    if (!emp) return;

    employeeForm.firstName.value = emp.firstName;
    employeeForm.lastName.value = emp.lastName;
    employeeForm.rank.value = emp.rank;
    employeeForm.idNumber.value = emp.idNumber;

    employeeModal.classList.remove("hidden");
    employeeModal.dataset.editingId = empId;
  }

  // Close modal
  function closeEmployeeModal() {
    employeeModal.classList.add("hidden");
    employeeModal.dataset.editingId = "";
  }

  // Add or update employee
  function onEmployeeFormSubmit(e) {
    e.preventDefault();
    const firstName = employeeForm.firstName.value.trim();
    const lastName = employeeForm.lastName.value.trim();
    const rank = employeeForm.rank.value.trim();
    const idNumber = employeeForm.idNumber.value.trim();

    if (!firstName || !lastName || !rank || !idNumber) return;

    const monthKey = formatMonthKey(currentYear, currentMonth);
    if (!attendanceData[monthKey]) {
      attendanceData[monthKey] = { employees: [] };
    }

    const editingId = employeeModal.dataset.editingId;

    if (editingId) {
      // Update existing employee
      const emp = attendanceData[monthKey].employees.find((e) => e.id === editingId);
      if (emp) {
        emp.firstName = firstName;
        emp.lastName = lastName;
        emp.rank = rank;
        emp.idNumber = idNumber;
        logActivity('تعديل موظف', `تم تعديل بيانات الموظف: ${firstName} ${lastName}`);
      }
    } else {
      // Add new employee
      attendanceData[monthKey].employees.push({
        id: uuidv4(),
        firstName,
        lastName,
        rank,
        idNumber,
        days: {},
      });
      logActivity('إضافة موظف', `تم إضافة موظف جديد: ${firstName} ${lastName} - ${rank}`);
    }

    saveData(attendanceData);
    closeEmployeeModal();
    renderTableBody(searchMainInput.value);
    checkAbsenceWarnings();
  }

  // Delete employee
  function deleteEmployee(empId) {
    if (!confirm("هل أنت متأكد من حذف هذا الموظف؟")) return;
    const monthKey = formatMonthKey(currentYear, currentMonth);
    if (!attendanceData[monthKey]) return;

    const emp = attendanceData[monthKey].employees.find(e => e.id === empId);
    if (emp) {
      logActivity('حذف موظف', `تم حذف الموظف: ${emp.firstName} ${emp.lastName}`);
    }

    attendanceData[monthKey].employees = attendanceData[monthKey].employees.filter((e) => e.id !== empId);
    saveData(attendanceData);
    renderTableBody(searchMainInput.value);
  }

  // On month or year change
  function onMonthYearChange() {
    currentYear = parseInt(yearSelect.value);
    currentMonth = parseInt(monthSelect.value);
    renderDaysHeader();
    renderTableBody(searchMainInput.value);
    checkAbsenceWarnings();
  }

  // Check absence warnings for employees with 3 or more absences
  function checkAbsenceWarnings() {
    const monthKey = formatMonthKey(currentYear, currentMonth);
    const monthData = attendanceData[monthKey];
    if (!monthData || !monthData.employees.length) return;

    monthData.employees.forEach(emp => {
      const totalAbsent = Object.values(emp.days || {}).filter((d) => d.absent).length;
      if (totalAbsent >= 3) {
        setTimeout(() => {
          alert(`يرجي اتخاذ الإجراءات الإدارية اللازمة ضد الموظف ${emp.firstName} ${emp.lastName}، عدد الغيابات: ${totalAbsent}`);
        }, 100);
      }
    });
  }

  // Helper: get month keys for last N months including current
  function getLastNMonthKeys(year, month, n) {
    const keys = [];
    let y = year;
    let m = month;
    for (let i = 0; i < n; i++) {
      keys.push(formatMonthKey(y, m));
      m--;
      if (m < 1) {
        m = 12;
        y--;
      }
    }
    return keys;
  }

  // Helper: get all month keys for a year
  function getYearMonthKeys(year) {
    const keys = [];
    for (let m = 1; m <= 12; m++) {
      keys.push(formatMonthKey(year, m));
    }
    return keys;
  }

  // Helper: get week start (Sunday) and end (Saturday) dates for current date
  function getWeekRange(date) {
    const day = date.getDay(); // 0 (Sun) to 6 (Sat)
    const diffToSunday = day; // days since Sunday
    const diffToSaturday = 6 - day;
    const sunday = new Date(date);
    sunday.setDate(date.getDate() - diffToSunday);
    sunday.setHours(0,0,0,0);
    const saturday = new Date(date);
    saturday.setDate(date.getDate() + diffToSaturday);
    saturday.setHours(23,59,59,999);
    return { sunday, saturday };
  }

  // Show statistics modal
  function openStatsModal() {
    renderStatsTable();
    statsModal.classList.remove("hidden");
    searchStatsInput.value = "";
  }

  // Close statistics modal
  function closeStatsModal() {
    statsModal.classList.add("hidden");
  }

  // Render statistics table with optional filter
  function renderStatsTable(filter = "") {
    statsTableBody.innerHTML = "";
    const now = new Date();
    const adjustedNow = new Date(now.getTime() + 60 * 60 * 1000); // add 1 hour delay
    const { sunday, saturday } = getWeekRange(adjustedNow);

    const filterLower = filter.trim().toLowerCase();

    // Get keys for last 3 months including current
    const last3MonthsKeys = getLastNMonthKeys(currentYear, currentMonth, 3);
    // Get keys for the whole year
    const yearKeys = getYearMonthKeys(currentYear);

    // Collect all employees from current month (to have list)
    const monthKey = formatMonthKey(currentYear, currentMonth);
    const monthData = attendanceData[monthKey];
    if (!monthData || !monthData.employees.length) {
      const tr = document.createElement("tr");
      const td = document.createElement("td");
      td.colSpan = 12;
      td.className = "py-6 text-center text-gray-500";
      td.textContent = "لا يوجد موظفين لهذا الشهر";
      tr.appendChild(td);
      statsTableBody.appendChild(tr);
      return;
    }

    const employees = monthData.employees;

    employees.forEach(emp => {
      // Filter by firstName, lastName, rank or idNumber
      if (
        filterLower &&
        !emp.firstName.toLowerCase().includes(filterLower) &&
        !emp.lastName.toLowerCase().includes(filterLower) &&
        !emp.rank.toLowerCase().includes(filterLower) &&
        !emp.idNumber.toLowerCase().includes(filterLower)
      ) {
        return;
      }

      // Calculate weekly stats (خصم الأسبوع, حفظ الأسبوع)
      let deductedWeek = 0;
      let savedWeek = 0;

      // We need to check days in current month and possibly previous month if week overlaps
      // We'll check days in current month and previous month if needed
      // For simplicity, check current month and previous month data if exists

      // Get current month data and previous month data
      const prevMonthDate = new Date(currentYear, currentMonth - 2, 1); // JS month 0-based
      const prevMonthKey = formatMonthKey(prevMonthDate.getFullYear(), prevMonthDate.getMonth() + 1);
      const currentMonthData = attendanceData[monthKey];
      const prevMonthData = attendanceData[prevMonthKey];

      // Helper to count days in a month for employee within week range
      function countWeekDays(empData, year, month) {
        if (!empData) return { deducted: 0, saved: 0 };
        let deducted = 0;
        let saved = 0;
        const daysInMonth = getDaysInMonth(year, month);
        for (let day = 1; day <= daysInMonth; day++) {
          const dayDate = new Date(year, month - 1, day);
          if (dayDate >= sunday && dayDate <= saturday) {
            const dayData = empData.days?.[day];
            if (dayData && dayData.absent) {
              if (dayData.deducted) deducted++;
              if (dayData.saved) saved++;
            }
          }
        }
        return { deducted, saved };
      }

      // Count for current month
      const empCurrentMonth = currentMonthData.employees.find(e => e.id === emp.id);
      const currentMonthWeekCounts = countWeekDays(empCurrentMonth, currentYear, currentMonth);

      // Count for previous month if week overlaps
      let prevMonthWeekCounts = { deducted: 0, saved: 0 };
      if (prevMonthData) {
        const prevYear = prevMonthDate.getFullYear();
        const prevMonth = prevMonthDate.getMonth() + 1;
        const empPrevMonth = prevMonthData.employees.find(e => e.id === emp.id);
        prevMonthWeekCounts = countWeekDays(empPrevMonth, prevYear, prevMonth);
      }

      deductedWeek = currentMonthWeekCounts.deducted + prevMonthWeekCounts.deducted;
      savedWeek = currentMonthWeekCounts.saved + prevMonthWeekCounts.saved;

      // Calculate monthly stats (خصم الشهر, حفظ الشهر)
      const totalDeductedMonth = empCurrentMonth ? Object.values(empCurrentMonth.days || {}).filter(d => d.deducted).length : 0;
      const totalSavedMonth = empCurrentMonth ? Object.values(empCurrentMonth.days || {}).filter(d => d.saved).length : 0;

      // Calculate last 3 months stats
      let totalDeducted3Months = 0;
      let totalSaved3Months = 0;
      last3MonthsKeys.forEach(key => {
        const data = attendanceData[key];
        if (!data) return;
        const empData = data.employees.find(e => e.id === emp.id);
        if (!empData) return;
        totalDeducted3Months += Object.values(empData.days || {}).filter(d => d.deducted).length;
        totalSaved3Months += Object.values(empData.days || {}).filter(d => d.saved).length;
      });

      // Calculate yearly stats
      let totalDeductedYear = 0;
      let totalSavedYear = 0;
      yearKeys.forEach(key => {
        const data = attendanceData[key];
        if (!data) return;
        const empData = data.employees.find(e => e.id === emp.id);
        if (!empData) return;
        totalDeductedYear += Object.values(empData.days || {}).filter(d => d.deducted).length;
        totalSavedYear += Object.values(empData.days || {}).filter(d => d.saved).length;
      });

      const tr = document.createElement("tr");
      tr.className = "hover:bg-indigo-50";

      const tdFirstName = document.createElement("td");
      tdFirstName.className = "border border-gray-300 px-4 py-2 truncate text-black";
      tdFirstName.textContent = emp.firstName;
      tr.appendChild(tdFirstName);

      const tdLastName = document.createElement("td");
      tdLastName.className = "border border-gray-300 px-4 py-2 truncate text-black";
      tdLastName.textContent = emp.lastName;
      tr.appendChild(tdLastName);

      const tdRank = document.createElement("td");
      tdRank.className = "border border-gray-300 px-4 py-2 truncate text-black";
      tdRank.textContent = emp.rank;
      tr.appendChild(tdRank);

      const tdIdNumber = document.createElement("td");
      tdIdNumber.className = "border border-gray-300 px-4 py-2 truncate text-black";
      tdIdNumber.textContent = emp.idNumber;
      tr.appendChild(tdIdNumber);

      const tdDeductedWeek = document.createElement("td");
      tdDeductedWeek.className = "border border-gray-300 px-4 py-2 font-semibold text-black";
      tdDeductedWeek.textContent = deductedWeek;
      tr.appendChild(tdDeductedWeek);

      const tdSavedWeek = document.createElement("td");
      tdSavedWeek.className = "border border-gray-300 px-4 py-2 font-semibold text-black";
      tdSavedWeek.textContent = savedWeek;
      tr.appendChild(tdSavedWeek);

      const tdDeductedMonth = document.createElement("td");
      tdDeductedMonth.className = "border border-gray-300 px-4 py-2 font-semibold text-black";
      tdDeductedMonth.textContent = totalDeductedMonth;
      tr.appendChild(tdDeductedMonth);

      const tdSavedMonth = document.createElement("td");
      tdSavedMonth.className = "border border-gray-300 px-4 py-2 font-semibold text-black";
      tdSavedMonth.textContent = totalSavedMonth;
      tr.appendChild(tdSavedMonth);

      const tdDeducted3Months = document.createElement("td");
      tdDeducted3Months.className = "border border-gray-300 px-4 py-2 font-semibold text-black";
      tdDeducted3Months.textContent = totalDeducted3Months;
      tr.appendChild(tdDeducted3Months);

      const tdSaved3Months = document.createElement("td");
      tdSaved3Months.className = "border border-gray-300 px-4 py-2 font-semibold text-black";
      tdSaved3Months.textContent = totalSaved3Months;
      tr.appendChild(tdSaved3Months);

      const tdDeductedYear = document.createElement("td");
      tdDeductedYear.className = "border border-gray-300 px-4 py-2 font-semibold text-black";
      tdDeductedYear.textContent = totalDeductedYear;
      tr.appendChild(tdDeductedYear);

      const tdSavedYear = document.createElement("td");
      tdSavedYear.className = "border border-gray-300 px-4 py-2 font-semibold text-black";
      tdSavedYear.textContent = totalSavedYear;
      tr.appendChild(tdSavedYear);

      statsTableBody.appendChild(tr);
    });

    if (!statsTableBody.hasChildNodes()) {
      const tr = document.createElement("tr");
      const td = document.createElement("td");
      td.colSpan = 12;
      td.className = "py-6 text-center text-gray-500";
      td.textContent = "لا توجد نتائج مطابقة للبحث";
      tr.appendChild(td);
      statsTableBody.appendChild(tr);
    }
  }

  // Activity Modal Functions
  function showActivityModal() {
    renderActivityList();
    activityModal.classList.remove("hidden");
  }

  function closeActivityModal() {
    activityModal.classList.add("hidden");
  }

  function renderActivityList() {
    const activities = getRecentActivities(50);
    activityList.innerHTML = "";

    if (activities.length === 0) {
      activityList.innerHTML = '<div class="text-center text-gray-500 py-8">لا توجد نشاطات مسجلة</div>';
      return;
    }

    activities.forEach(activity => {
      const activityDiv = document.createElement("div");
      activityDiv.className = "bg-gray-50 rounded-lg p-4 border-l-4 border-indigo-500";

      const actionColor = activity.action === 'تسجيل دخول' ? 'text-green-600' :
                         activity.action === 'تسجيل خروج' ? 'text-red-600' :
                         activity.action === 'إضافة موظف' ? 'text-blue-600' :
                         activity.action === 'حذف موظف' ? 'text-red-600' :
                         activity.action === 'تعديل موظف' ? 'text-orange-600' :
                         'text-purple-600';

      activityDiv.innerHTML = `
        <div class="flex justify-between items-start">
          <div class="flex-1">
            <div class="flex items-center gap-2 mb-1">
              <span class="font-semibold ${actionColor}">${activity.action}</span>
              <span class="text-sm text-gray-500">بواسطة ${activity.displayName}</span>
            </div>
            <p class="text-gray-700 text-sm">${activity.details}</p>
          </div>
          <div class="text-xs text-gray-500 text-left">
            <div>${activity.date}</div>
            <div>${activity.time}</div>
          </div>
        </div>
      `;

      activityList.appendChild(activityDiv);
    });
  }

  function clearActivityLog() {
    if (confirm("هل أنت متأكد من مسح جميع النشاطات المسجلة؟")) {
      localStorage.removeItem('userActivities');
      logActivity('مسح السجل', 'تم مسح جميع النشاطات المسجلة');
      renderActivityList();
    }
  }

  // User Management Functions
  function showUserManagementModal() {
    renderUsersTable();
    userManagementModal.classList.remove("hidden");
  }

  function closeUserManagementModal() {
    userManagementModal.classList.add("hidden");
  }

  function renderUsersTable() {
    const users = getUsers();
    usersTableBody.innerHTML = "";

    Object.keys(users).forEach(username => {
      const user = users[username];
      const tr = document.createElement("tr");
      tr.className = "hover:bg-gray-50";

      const roleText = user.role === 'admin' ? 'مدير' : 'موظف';
      const roleColor = user.role === 'admin' ? 'text-red-600 font-semibold' : 'text-blue-600';

      tr.innerHTML = `
        <td class="border border-gray-300 px-4 py-2 font-semibold text-black">${username}</td>
        <td class="border border-gray-300 px-4 py-2 text-black">${user.displayName}</td>
        <td class="border border-gray-300 px-4 py-2 ${roleColor}">${roleText}</td>
        <td class="border border-gray-300 px-4 py-2 text-black">••••••••</td>
        <td class="border border-gray-300 px-4 py-2">
          <div class="flex justify-center gap-2">
            <button onclick="editUser('${username}')" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition duration-300">
              <i class="fas fa-edit"></i> تعديل
            </button>
            ${username !== 'ISMAIL' ? `
              <button onclick="deleteUser('${username}')" class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition duration-300">
                <i class="fas fa-trash"></i> حذف
              </button>
            ` : ''}
          </div>
        </td>
      `;

      usersTableBody.appendChild(tr);
    });
  }

  function addUser(username, password, displayName) {
    const users = getUsers();

    if (users[username.toUpperCase()]) {
      alert('اسم المستخدم موجود بالفعل!');
      return false;
    }

    users[username.toUpperCase()] = {
      password: password,
      role: 'user',
      displayName: displayName
    };

    saveUsers(users);
    logActivity('إضافة مستخدم', `تم إضافة مستخدم جديد: ${displayName} (${username.toUpperCase()})`);
    renderUsersTable();
    return true;
  }

  function editUser(username) {
    const users = getUsers();
    const user = users[username];

    if (!user) return;

    document.getElementById('editUsername').value = username;
    document.getElementById('editPassword').value = '';
    document.getElementById('editDisplayName').value = user.displayName;

    editUserModal.classList.remove("hidden");
    editUserModal.dataset.editingUser = username;
  }

  function updateUser(username, newPassword, newDisplayName) {
    const users = getUsers();

    if (!users[username]) return false;

    if (newPassword.trim()) {
      users[username].password = newPassword.trim();
    }
    users[username].displayName = newDisplayName.trim();

    saveUsers(users);
    logActivity('تعديل مستخدم', `تم تعديل بيانات المستخدم: ${newDisplayName} (${username})`);
    renderUsersTable();
    return true;
  }

  function deleteUser(username) {
    if (username === 'ISMAIL') {
      alert('لا يمكن حذف المدير الرئيسي!');
      return;
    }

    if (!confirm(`هل أنت متأكد من حذف المستخدم: ${username}؟`)) return;

    const users = getUsers();
    const user = users[username];

    if (user) {
      delete users[username];
      saveUsers(users);
      logActivity('حذف مستخدم', `تم حذف المستخدم: ${user.displayName} (${username})`);
      renderUsersTable();
    }
  }

  function closeEditUserModal() {
    editUserModal.classList.add("hidden");
    editUserModal.dataset.editingUser = "";
  }

  // Inquiry Functions
  function openEmployeeInquiry(empId) {
    const inquiryData = getEmployeeInquiry(empId);

    if (!inquiryData || !inquiryData.link) {
      // No file saved, open modal to set one
      alert('لم يتم ربط أي ملف بهذا الموظف. انقر بالزر الأيمن لاختيار ملف.');
      openInquiryModal(empId);
      return;
    }

    const filePath = inquiryData.link.trim();

    try {
      // Check if it's a web URL
      if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
        window.open(filePath, '_blank');
      }
      // Check if it's a full file path
      else if (filePath.includes('\\') || filePath.includes('/')) {
        // For local files, we'll use file:// protocol
        const fileUrl = filePath.startsWith('file://') ? filePath : `file:///${filePath.replace(/\\/g, '/')}`;
        window.open(fileUrl, '_blank');
      }
      // If it's just a filename, show instructions
      else {
        alert(`الملف المرتبط: ${filePath}\n\nلفتح الملف، تأكد من أن الملف موجود في نفس المكان الذي اخترته منه.\n\nإذا تم نقل الملف، انقر بالزر الأيمن لاختيار الملف من موقعه الجديد.`);

        // Try to open anyway in case the browser can handle it
        const fileUrl = `file:///${filePath}`;
        window.open(fileUrl, '_blank');
      }

      const monthKey = formatMonthKey(currentYear, currentMonth);
      const emp = attendanceData[monthKey].employees.find((e) => e.id === empId);
      if (emp) {
        logActivity('فتح ملف استفسار', `تم فتح ملف استفسار للموظف: ${emp.firstName} ${emp.lastName} - ${filePath}`);
      }
    } catch (error) {
      alert(`حدث خطأ في فتح الملف: ${filePath}\n\nتأكد من:\n1. وجود الملف في موقعه الأصلي\n2. أن لديك الصلاحيات لفتح الملف\n3. أن البرنامج المناسب مثبت لفتح هذا النوع من الملفات\n\nانقر بالزر الأيمن لاختيار ملف جديد.`);
    }
  }

  function openInquiryModal(empId) {
    const monthKey = formatMonthKey(currentYear, currentMonth);
    const emp = attendanceData[monthKey].employees.find((e) => e.id === empId);
    if (!emp) return;

    // Populate employee info
    employeeInfo.innerHTML = `
      <div><strong>الاسم:</strong> ${emp.firstName} ${emp.lastName}</div>
      <div><strong>الرتبة:</strong> ${emp.rank}</div>
      <div><strong>رقم الذاتية:</strong> ${emp.idNumber}</div>
    `;

    // Load saved inquiry data if exists
    const inquiryData = getEmployeeInquiry(empId);
    if (inquiryData) {
      inquiryLink.value = inquiryData.link || '';
      inquiryNotes.value = inquiryData.notes || '';
    } else {
      inquiryLink.value = '';
      inquiryNotes.value = '';
    }

    inquiryModal.classList.remove("hidden");
    inquiryModal.dataset.employeeId = empId;
  }

  function closeInquiryModal() {
    inquiryModal.classList.add("hidden");
    inquiryModal.dataset.employeeId = "";
  }

  function saveEmployeeInquiry(empId, link, notes) {
    const inquiries = JSON.parse(localStorage.getItem('employeeInquiries') || '{}');
    inquiries[empId] = {
      link: link,
      notes: notes,
      lastUpdated: new Date().toISOString()
    };
    localStorage.setItem('employeeInquiries', JSON.stringify(inquiries));

    const monthKey = formatMonthKey(currentYear, currentMonth);
    const emp = attendanceData[monthKey].employees.find((e) => e.id === empId);
    if (emp) {
      logActivity('حفظ استفسار', `تم حفظ رابط استفسار للموظف: ${emp.firstName} ${emp.lastName}`);
    }
  }

  function getEmployeeInquiry(empId) {
    const inquiries = JSON.parse(localStorage.getItem('employeeInquiries') || '{}');
    return inquiries[empId] || null;
  }

  function openInquiryLink() {
    const filePath = inquiryLink.value.trim();
    if (!filePath) {
      alert('يرجى اختيار ملف أولاً');
      return;
    }

    try {
      // Check if it's a web URL
      if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
        window.open(filePath, '_blank');
      }
      // Check if it's a file path
      else if (filePath.includes('\\') || filePath.includes('/')) {
        // For local files, we'll use file:// protocol
        const fileUrl = filePath.startsWith('file://') ? filePath : `file:///${filePath.replace(/\\/g, '/')}`;
        window.open(fileUrl, '_blank');
      }
      // Default case - try to open as is
      else {
        const fileUrl = `file:///${filePath}`;
        window.open(fileUrl, '_blank');
      }

      const empId = inquiryModal.dataset.employeeId;
      const monthKey = formatMonthKey(currentYear, currentMonth);
      const emp = attendanceData[monthKey].employees.find((e) => e.id === empId);
      if (emp) {
        logActivity('فتح ملف من النافذة', `تم فتح ملف للموظف: ${emp.firstName} ${emp.lastName} - ${filePath}`);
      }
    } catch (error) {
      alert('حدث خطأ في فتح الملف. تأكد من وجود الملف في موقعه الأصلي.');
    }
  }

  function browseForFile() {
    // Create a hidden file input
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.style.display = 'none';

    // Accept common file types
    fileInput.accept = '.doc,.docx,.pdf,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif,.mp4,.avi,.zip,.rar';

    fileInput.addEventListener('change', (e) => {
      if (e.target.files.length > 0) {
        const file = e.target.files[0];

        // For web browsers, we can only get the file name, not the full path for security reasons
        // We'll store the file name and show a message about file location
        if (file.webkitRelativePath || file.path) {
          inquiryLink.value = file.webkitRelativePath || file.path || file.name;
        } else {
          // For security reasons, browsers don't expose full file paths
          // We'll use a different approach - store file info and handle it specially
          inquiryLink.value = file.name;
          inquiryLink.dataset.fileSize = file.size;
          inquiryLink.dataset.fileType = file.type;

          // Show user a message about file selection
          alert(`تم اختيار الملف: ${file.name}\n\nملاحظة: لأسباب أمنية، سيتم فتح الملف من موقعه الأصلي. تأكد من عدم نقل الملف من مكانه.`);
        }
      }
    });

    document.body.appendChild(fileInput);
    fileInput.click();
    document.body.removeChild(fileInput);
  }

  // Initialize app
  function init() {
    // Always add login form event listener first
    loginForm.addEventListener("submit", handleLoginSubmit);

    // Check authentication first
    if (!checkAuth()) {
      showLoginScreen();
      setupEventListeners(); // Setup other event listeners
      return;
    }

    // User is authenticated, show main app
    hideLoginScreen();
    currentUserName.textContent = currentUser.displayName;

    // Show manage users button only for admin
    if (currentUser.role === 'admin') {
      manageUsersBtn.style.display = 'flex';
    } else {
      manageUsersBtn.style.display = 'none';
    }

    // Show recent activities if different user
    showRecentActivitiesIfNeeded();

    initMonthYearSelectors();
    renderDaysHeader();
    renderTableBody();
    checkAbsenceWarnings();
    setupEventListeners(); // Setup other event listeners
  }

  // Setup all event listeners
  function setupEventListeners() {

    // Main app event listeners
    addEmployeeBtn.addEventListener("click", openAddEmployeeModal);
    cancelEmployeeBtn.addEventListener("click", closeEmployeeModal);
    closeModalBtn.addEventListener("click", closeEmployeeModal);
    employeeForm.addEventListener("submit", onEmployeeFormSubmit);
    monthSelect.addEventListener("change", onMonthYearChange);
    yearSelect.addEventListener("change", onMonthYearChange);
    statsBtn.addEventListener("click", openStatsModal);
    closeStatsBtn.addEventListener("click", closeStatsModal);

    // New event listeners
    logoutBtn.addEventListener("click", handleLogoutClick);
    activityBtn.addEventListener("click", showActivityModal);
    closeActivityBtn.addEventListener("click", closeActivityModal);
    clearActivityBtn.addEventListener("click", clearActivityLog);

    // User management event listeners
    manageUsersBtn.addEventListener("click", showUserManagementModal);
    closeUserManagementBtn.addEventListener("click", closeUserManagementModal);
    cancelEditUserBtn.addEventListener("click", closeEditUserModal);

    // Inquiry modal event listeners
    cancelInquiryBtn.addEventListener("click", closeInquiryModal);
    openLinkBtn.addEventListener("click", openInquiryLink);
    browseFileBtn.addEventListener("click", browseForFile);
    saveInquiryBtn.addEventListener("click", () => {
      const empId = inquiryModal.dataset.employeeId;
      const link = inquiryLink.value.trim();
      const notes = inquiryNotes.value.trim();

      if (link || notes) {
        saveEmployeeInquiry(empId, link, notes);
        alert('تم ربط الملف بالموظف بنجاح! الآن يمكنك الضغط على الأيقونة لفتح الملف مباشرة.');
        closeInquiryModal();
      } else {
        alert('يرجى اختيار ملف أو إدخال ملاحظات على الأقل');
      }
    });

    // Add user form
    addUserForm.addEventListener("submit", (e) => {
      e.preventDefault();
      const username = document.getElementById('newUsername').value.trim();
      const password = document.getElementById('newPassword').value.trim();
      const displayName = document.getElementById('newDisplayName').value.trim();

      if (addUser(username, password, displayName)) {
        addUserForm.reset();
      }
    });

    // Edit user form
    editUserForm.addEventListener("submit", (e) => {
      e.preventDefault();
      const username = editUserModal.dataset.editingUser;
      const password = document.getElementById('editPassword').value;
      const displayName = document.getElementById('editDisplayName').value.trim();

      if (updateUser(username, password, displayName)) {
        closeEditUserModal();
      }
    });

    // Delegate select changes on table body
    tableBody.addEventListener("change", (e) => {
      if (e.target.tagName === "SELECT") {
        onSelectChange(e);
      }
    });

    // Search button click event on main search
    searchMainBtn.addEventListener("click", () => {
      renderTableBody(searchMainInput.value);
    });

    // Also allow Enter key on main search input
    searchMainInput.addEventListener("keydown", (e) => {
      if (e.key === "Enter") {
        e.preventDefault();
        renderTableBody(searchMainInput.value);
      }
    });

    // Search button click event on stats modal
    searchStatsBtn.addEventListener("click", () => {
      renderStatsTable(searchStatsInput.value);
    });

    // Also allow Enter key on stats search input
    searchStatsInput.addEventListener("keydown", (e) => {
      if (e.key === "Enter") {
        e.preventDefault();
        renderStatsTable(searchStatsInput.value);
      }
    });
  }

  // Login/Logout UI Functions
  function showLoginScreen() {
    loginScreen.classList.remove("hidden");
  }

  function hideLoginScreen() {
    loginScreen.classList.add("hidden");
  }

  function handleLoginSubmit(e) {
    e.preventDefault();
    const username = document.getElementById("username").value.trim();
    const password = document.getElementById("password").value.trim();

    if (handleLogin(username, password)) {
      hideLoginScreen();
      currentUserName.textContent = currentUser.displayName;

      // Show manage users button only for admin
      if (currentUser.role === 'admin') {
        manageUsersBtn.style.display = 'flex';
      } else {
        manageUsersBtn.style.display = 'none';
      }

      showRecentActivitiesIfNeeded();

      // Initialize main app
      initMonthYearSelectors();
      renderDaysHeader();
      renderTableBody();
      checkAbsenceWarnings();

      // Clear login form
      loginForm.reset();
      hideLoginError();
    } else {
      showLoginError("اسم المستخدم أو كلمة المرور غير صحيحة");
    }
  }

  function handleLogoutClick() {
    if (confirm("هل أنت متأكد من تسجيل الخروج؟")) {
      handleLogout();
      showLoginScreen();
      loginForm.reset();
      hideLoginError();
    }
  }

  function showLoginError(message) {
    loginErrorText.textContent = message;
    loginError.classList.remove("hidden");
  }

  function hideLoginError() {
    loginError.classList.add("hidden");
  }

  window.addEventListener("DOMContentLoaded", init);
</script>

</body>
</html>html>